#!/bin/bash

# 四层视频架构验证脚本
# 验证所有四层是否正常工作

echo "🎬 四层视频架构验证开始..."
echo "时间: $(date)"
echo "=========================================="

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 验证函数
verify_layer() {
    local layer_name="$1"
    local test_url="$2"
    local timeout="$3"
    
    echo -e "${BLUE}检查 $layer_name 层...${NC}"
    
    if curl -I -s --max-time "$timeout" "$test_url" | head -1 | grep -q "200\|206"; then
        echo -e "${GREEN}✅ $layer_name 层: 正常${NC}"
        return 0
    else
        echo -e "${RED}❌ $layer_name 层: 异常${NC}"
        return 1
    fi
}

# 验证配置API
echo -e "${BLUE}1. 验证配置API...${NC}"
if curl -s https://love.yuh.cool/api/config | jq -e '.data.videoDelivery.enabled' > /dev/null; then
    echo -e "${GREEN}✅ 配置API: 正常，四层架构已启用${NC}"
else
    echo -e "${RED}❌ 配置API: 异常${NC}"
    exit 1
fi

echo ""

# 验证第一层: Cloudflare R2
echo -e "${BLUE}2. 验证第一层: Cloudflare R2${NC}"
verify_layer "R2" "https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/videos/home.mp4" 6
r2_status=$?

echo ""

# 验证第二层: Cloudinary
echo -e "${BLUE}3. 验证第二层: Cloudinary${NC}"
verify_layer "Cloudinary" "https://res.cloudinary.com/dcglebc2w/video/upload/love-website/home.mp4" 7
cloudinary_status=$?

echo ""

# 验证第三层: VPS
echo -e "${BLUE}4. 验证第三层: VPS本地${NC}"
verify_layer "VPS" "https://love.yuh.cool/video/compressed/home.mp4" 10
vps_status=$?

echo ""

# 验证第四层: 星空背景
echo -e "${BLUE}5. 验证第四层: 星空背景${NC}"
if curl -s https://love.yuh.cool/src/client/styles/starry-background.css | head -10 | grep -q "星空\|背景\|保障"; then
    echo -e "${GREEN}✅ 星空背景层: 正常${NC}"
    starry_status=0
else
    echo -e "${RED}❌ 星空背景层: 异常${NC}"
    starry_status=1
fi

echo ""

# 验证智能加载器
echo -e "${BLUE}6. 验证智能加载器...${NC}"
if curl -s https://love.yuh.cool/src/client/scripts/video-loader.js | grep -q "VideoLoader\|loadVideo"; then
    echo -e "${GREEN}✅ 智能加载器: 正常${NC}"
else
    echo -e "${RED}❌ 智能加载器: 异常${NC}"
fi

echo ""

# 验证测试页面
echo -e "${BLUE}7. 验证测试页面...${NC}"
test_pages=(
    "r2-video-loading-test.html"
    "cloudinary-multi-account-test.html"
    "vps-video-loading-test.html"
    "starry-background-fallback-test.html"
)

for page in "${test_pages[@]}"; do
    if curl -s "https://love.yuh.cool/test/$page" | grep -q "html\|test"; then
        echo -e "${GREEN}✅ 测试页面 $page: 可访问${NC}"
    else
        echo -e "${YELLOW}⚠️ 测试页面 $page: 可能不存在${NC}"
    fi
done

echo ""

# 总结报告
echo "=========================================="
echo -e "${BLUE}📊 四层架构验证总结:${NC}"

total_layers=4
working_layers=0

if [ $r2_status -eq 0 ]; then
    working_layers=$((working_layers + 1))
fi

if [ $cloudinary_status -eq 0 ]; then
    working_layers=$((working_layers + 1))
fi

if [ $vps_status -eq 0 ]; then
    working_layers=$((working_layers + 1))
fi

if [ $starry_status -eq 0 ]; then
    working_layers=$((working_layers + 1))
fi

echo "正常工作层数: $working_layers/$total_layers"

if [ $working_layers -eq $total_layers ]; then
    echo -e "${GREEN}🎉 四层架构完全正常！99.9%可用性达成！${NC}"
    exit 0
elif [ $working_layers -ge 3 ]; then
    echo -e "${YELLOW}⚠️ 四层架构基本正常，有 $((total_layers - working_layers)) 层异常${NC}"
    exit 0
elif [ $working_layers -ge 1 ]; then
    echo -e "${YELLOW}⚠️ 部分层级工作正常，建议检查异常层级${NC}"
    exit 1
else
    echo -e "${RED}❌ 四层架构严重异常，需要立即修复！${NC}"
    exit 1
fi
